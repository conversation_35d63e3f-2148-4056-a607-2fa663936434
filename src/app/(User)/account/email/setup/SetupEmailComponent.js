'use client';
import { Col, Row } from "react-bootstrap";
import React, { useState, useEffect } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import TextInput from "@/Components/UI/TextInput";
import StatusIndicator from "@/Components/UI/StatusIndicator";
import { put, get } from "@/utils/apiUtils";
import { useRouter, useSearchParams } from "next/navigation";
import { useSelector, useDispatch } from 'react-redux';
import { setUser } from '@/redux/authSlice';
import "@/css/account/AccountDetails.scss";

export default function SetupEmailComponent() {
    const [email, setEmail] = useState('');
    const [originalEmail, setOriginalEmail] = useState('');
    const [userData, setUserData] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    const [isLoadingUserData, setIsLoadingUserData] = useState(true);
    const [saveStatus, setSaveStatus] = useState(null);
    const [error, setError] = useState(null);
    
    // Validation state
    const [validationError, setValidationError] = useState(null);
    const [isTouched, setIsTouched] = useState(false);

    const router = useRouter();
    const searchParams = useSearchParams();
    const dispatch = useDispatch();
    const reduxUser = useSelector((state) => state?.auth?.user || null);

    // Fetch user data to pre-populate email
    const fetchUserData = async () => {
        try {
            setIsLoadingUserData(true);
            const response = await get('/account');

            if (response.success && response.data) {
                setUserData(response.data);
                const existingEmail = response.data.email || '';
                setEmail(existingEmail);
                setOriginalEmail(existingEmail);

                // Update Redux store
                dispatch(setUser(response.data));
                localStorage.setItem('user', JSON.stringify(response.data));
            }
        } catch (error) {
            console.error('Error fetching user data:', error);
            // Fallback to Redux user data
            if (reduxUser) {
                setUserData(reduxUser);
                const existingEmail = reduxUser.email || '';
                setEmail(existingEmail);
                setOriginalEmail(existingEmail);
            }
        } finally {
            setIsLoadingUserData(false);
        }
    };

    // Email validation function
    const validateEmail = (email) => {
        if (!email || email.trim().length === 0) {
            return 'Email address is required';
        }

        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email.trim())) {
            return 'Please enter a valid email address';
        }

        return null;
    };

    // Get redirect URL from search params or default
    const getRedirectUrl = () => {
        const from = searchParams.get('from');
        return from ? decodeURIComponent(from) : '/account/details';
    };

    useEffect(() => {
        fetchUserData();
    }, []);

    // Validate email when it changes
    useEffect(() => {
        if (email && isTouched) {
            const error = validateEmail(email);
            setValidationError(error);
        }
    }, [email, isTouched]);

    const metaArray = {
        noindex: true,
        title: "Setup Email Address | Update Info | TradeReply",
        description: "Update your email address on TradeReply.com.",
        canonical_link: "https://www.tradereply.com/account/details",
        og_site_name: "TradeReply",
        og_title: "Setup Email Address | Update Info | TradeReply",
        og_description: "Update your email address on TradeReply.com.",
        twitter_title: "Setup Email Address | Update Info | TradeReply",
        twitter_description: "Update your email address on TradeReply.com.",
    };

    const handleEmailChange = (e) => {
        const value = e.target.value;
        setEmail(value);
        setIsTouched(true);

        const error = validateEmail(value);
        setValidationError(error);

        // Clear save status when user types
        if (saveStatus) {
            setSaveStatus(null);
            setError(null);
        }
    };

    const handleSave = async () => {
        // Validate email before saving
        const validationErr = validateEmail(email);
        if (validationErr) {
            setSaveStatus('error');
            setError(validationErr);
            setValidationError(validationErr);
            setTimeout(() => setSaveStatus(null), 3000);
            return;
        }

        // Check if email has actually changed
        if (email.trim() === originalEmail) {
            setSaveStatus('error');
            setError('Please enter a different email address to update');
            setTimeout(() => setSaveStatus(null), 3000);
            return;
        }

        try {
            setIsLoading(true);
            setSaveStatus('loading');
            setError(null);
            setValidationError(null);

            // For now, let's use a simpler approach - update via the general account update endpoint
            const response = await put(`/account/update/${userData.id}`, {
                email: email.trim(),
            });

            if (response.success) {
                setSaveStatus('success');

                // Update local state with fresh user data
                if (response.data && response.data.user) {
                    setUserData(response.data.user);
                    dispatch(setUser(response.data.user));
                    localStorage.setItem('user', JSON.stringify(response.data.user));
                }

                // Get the appropriate redirect URL
                const redirectUrl = getRedirectUrl();
                console.log('Email update success - redirecting to:', redirectUrl);

                // Redirect back to the original page after 2 seconds
                setTimeout(() => {
                    router.push(redirectUrl);
                }, 2000);
            } else {
                throw new Error(response.message || 'Failed to update email address');
            }
        } catch (err) {
            console.error('Email update error:', err);
            const errorMessage = err.response?.data?.message || err.message || 'Failed to update email address. Please try again.';
            setSaveStatus('error');
            setError(errorMessage);
            setTimeout(() => setSaveStatus(null), 3000);
        } finally {
            setIsLoading(false);
        }
    };

    const handleCancel = () => {
        // Reset to original email or empty if no original
        setEmail(originalEmail);
        setError(null);
        setSaveStatus(null);
        setValidationError(null);
        setIsTouched(false);

        // Navigate back to the referring page
        const redirectUrl = getRedirectUrl();
        router.push(redirectUrl);
    };

    const isUpdating = originalEmail.length > 0;
    const hasChanges = email.trim() !== originalEmail;

    if (isLoadingUserData) {
        return (
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="account_setup_email">
                    <SidebarHeading title={isUpdating ? "Update Email Address" : "New Email Address"} />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <div className="account_header_main">
                                        <h6>Email</h6>
                                    </div>
                                    <p>Loading your current email information...</p>
                                </div>
                            </div>
                            <div className="common_blackcard_innerbody">
                                <div className="account_card_list">
                                    <div className="col-lg-5 col-md-8 col-12">
                                        <TextInput
                                            type="email"
                                            placeholder="Loading..."
                                            value=""
                                            disabled={true}
                                        />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </AccountLayout>
        );
    }

    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="account_setup_email">
                    <SidebarHeading title={isUpdating ? "Update Email Address" : "New Email Address"} />
                    <div className="mb-3 mb-lg-4">
                        <div className="common_blackcard account_card">
                            <div className="common_blackcard_innerheader">
                                <div className="common_blackcard_innerheader_content">
                                    <div className="account_header_main">
                                        <h6>Email</h6>
                                        <div className="account_status_indicator">
                                            <StatusIndicator
                                                saveStatus={saveStatus}
                                                error={error}
                                            />
                                        </div>
                                    </div>
                                    <p>
                                        {isUpdating 
                                            ? "Update the email address associated with your TradeReply account. You will use this email address to log in."
                                            : "Enter the email address you want associated with your TradeReply account. You will use this email address to log in."
                                        }
                                    </p>
                                </div>
                            </div>
                            <div className="common_blackcard_innerbody">
                                <div className="account_card_list">
                                    <div className="col-lg-5 col-md-8 col-12">
                                        <TextInput
                                            type="email"
                                            placeholder="Enter your email address"
                                            value={email}
                                            onChange={handleEmailChange}
                                            disabled={isLoading || isLoadingUserData}
                                        />
                                    </div>

                                    {validationError && isTouched && (
                                        <div className="mt-3">
                                            <p style={{ color: 'red', fontSize: '14px' }}>
                                                {validationError}
                                            </p>
                                        </div>
                                    )}

                                    {error && saveStatus !== 'loading' && !validationError && (
                                        <div className="mt-3">
                                            <p style={{ color: 'red', fontSize: '14px' }}>
                                                {error}
                                            </p>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>
                        <div className="account_card_list_btns mt-3">
                            <button
                                className="btn-style white-btn"
                                onClick={handleCancel}
                                disabled={isLoading}
                            >
                                Cancel
                            </button>
                            <button
                                className="btn-style"
                                onClick={handleSave}
                                disabled={isLoading || !email.trim() || validationError || !hasChanges}
                            >
                                {isLoading ? 'Saving...' : (isUpdating ? 'Update Email' : 'Save Email')}
                            </button>
                        </div>
                    </div>

                </div>
            </AccountLayout>
        </>
    )
}
