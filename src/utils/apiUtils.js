import axiosInstance from './axiosInstance';

// GET Request Utility
export const get = async (url, params = {}) => {
  try {
    const response = await axiosInstance.get(url, { params });
    return response.data;
  } catch (error) {
    console.error('Error with GET request:', error);
    throw error; // Re-throw for further handling
  }
};
export const getBrokers = async (url, params = {}) => {
  try {
    const response = await axiosInstance.get(url, { params });
    return response.data;
  } catch (error) {
    console.error('Error with GET request:', error);
    throw error; // Re-throw for further handling
  }
};

// POST Request Utility
export const post = async (url, data) => {
  try {
    const response = await axiosInstance.post(url, data);
    return response.data;
  } catch (error) {
    console.error('Error with POST request:', error);
    throw error;
  }
};

// PUT Request Utility
export const put = async (url, data) => {
  try {
    const response = await axiosInstance.put(url, data);
    return response.data;
  } catch (error) {
    console.error('Error with PUT request:', error);
    throw error;
  }
};

// DELETE Request Utility
export const deleteRequest = async (url) => {
  try {
    const response = await axiosInstance.delete(url);
    return response.data;
  } catch (error) {
    console.error('Error with DELETE request:', error);
    throw error;
  }
};

// Username management utilities
export const updateUsername = async (username) => {
  try {
    const response = await axiosInstance.post('/username/update', { username });
    return response.data;
  } catch (error) {
    console.error('Error updating username:', error);
    throw error;
  }
};

export const checkUsernameAvailability = async (username) => {
  try {
    const response = await axiosInstance.post('/username/check-availability', { username });
    return response.data;
  } catch (error) {
    console.error('Error checking username availability:', error);
    throw error;
  }
};

// Email management utilities
export const updateEmail = async (newEmail) => {
  try {
    // For now, we'll use a simple approach that bypasses the complex email verification
    // This is a temporary solution until the backend email update flow is simplified
    const response = await axiosInstance.put('/account/update/current', {
      email: newEmail,
    });
    return response.data;
  } catch (error) {
    console.error('Error updating email:', error);
    throw error;
  }
};